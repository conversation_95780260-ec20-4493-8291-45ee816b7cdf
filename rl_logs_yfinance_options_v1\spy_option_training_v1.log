2025-09-21 13:42:34,511 - INFO - SPY.py:1932 - Logging configured for Option Trader v1.
2025-09-21 13:42:34,512 - INFO - SPY.py:1933 - Log file: C:\Users\<USER>\Desktop\All use default\rl_logs_yfinance_options_v1\spy_option_training_v1.log
2025-09-21 13:42:34,512 - INFO - SPY.py:1934 - Tickers to fetch: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-09-21 13:42:34,512 - INFO - SPY.py:1942 - YFinance session management disabled - letting yfinance handle its own sessions with curl_cffi.
2025-09-21 13:42:34,514 - INFO - SPY.py:4771 - --- Option Trader v3.1 ---
2025-09-21 13:42:34,514 - INFO - SPY.py:4781 - Signal generation mode...
2025-09-21 13:42:53,949 - INFO - SPY.py:2081 - Using min_periods_override=50 for SPY
2025-09-21 13:42:53,953 - INFO - SPY.py:2229 - Skipping additional feature calculations for SPY - using reduced feature set
2025-09-21 13:42:53,953 - INFO - SPY.py:2232 - SPY: Keeping columns ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY']
2025-09-21 13:42:53,956 - INFO - SPY.py:2260 - Processed SPY data shape: (63, 5). Columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY']
2025-09-21 13:42:53,956 - INFO - SPY.py:2261 - Data quality check: 0/315 (0.0%) zero values
2025-09-21 13:42:53,956 - INFO - SPY.py:712 - [INFO] PerformanceMonitor: Operation fetch_refactored_SPY_1758433354 SUCCESS in 19.44s [Ticker: SPY] [Operation: refactored_fetch]
2025-09-21 13:42:54,758 - INFO - SPY.py:1760 - Attempt 1/3 with timeout 20s
2025-09-21 13:42:59,016 - INFO - SPY.py:2081 - Using min_periods_override=50 for ^VIX
2025-09-21 13:42:59,017 - INFO - SPY.py:2105 - Processing ^VIX data with shape (63, 7)
2025-09-21 13:42:59,017 - INFO - SPY.py:2106 - Raw ^VIX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-09-21 13:42:59,017 - INFO - SPY.py:2108 - Raw ^VIX Close values - first: 20.510000228881836, last: 20.81999969482422
2025-09-21 13:42:59,019 - INFO - SPY.py:2028 - Applying VIX validation for VIX
2025-09-21 13:42:59,019 - INFO - SPY.py:2187 - Processed VIX close values - first: 20.510000228881836, last: 20.81999969482422
2025-09-21 13:42:59,020 - INFO - SPY.py:2220 - Market index ^VIX: Keeping only ['close_VIX']
2025-09-21 13:42:59,021 - INFO - SPY.py:2260 - Processed ^VIX data shape: (63, 1). Columns: ['close_VIX']
2025-09-21 13:42:59,021 - INFO - SPY.py:2261 - Data quality check: 0/63 (0.0%) zero values
2025-09-21 13:42:59,021 - INFO - SPY.py:982 - VALIDATION: ^VIX last value: 20.81999969482422
2025-09-21 13:42:59,022 - INFO - SPY.py:712 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX_1758433373 SUCCESS in 5.07s [Ticker: ^VIX] [Operation: refactored_fetch]
2025-09-21 13:42:59,824 - INFO - SPY.py:1760 - Attempt 1/3 with timeout 25s
2025-09-21 13:43:01,439 - INFO - SPY.py:2081 - Using min_periods_override=50 for ^VIX3M
2025-09-21 13:43:01,442 - INFO - SPY.py:2220 - Market index ^VIX3M: Keeping only ['close_VIX3M']
2025-09-21 13:43:01,443 - INFO - SPY.py:2260 - Processed ^VIX3M data shape: (63, 1). Columns: ['close_VIX3M']
2025-09-21 13:43:01,443 - INFO - SPY.py:2261 - Data quality check: 0/63 (0.0%) zero values
2025-09-21 13:43:01,444 - INFO - SPY.py:982 - VALIDATION: ^VIX3M last value: 22.6200008392334
2025-09-21 13:43:01,444 - INFO - SPY.py:712 - [INFO] PerformanceMonitor: Operation fetch_refactored_^VIX3M_1758433379 SUCCESS in 2.42s [Ticker: ^VIX3M] [Operation: refactored_fetch]
2025-09-21 13:43:02,246 - INFO - SPY.py:1760 - Attempt 1/3 with timeout 20s
2025-09-21 13:43:04,279 - INFO - SPY.py:2081 - Using min_periods_override=50 for ^IRX
2025-09-21 13:43:04,279 - INFO - SPY.py:2105 - Processing ^IRX data with shape (63, 7)
2025-09-21 13:43:04,279 - INFO - SPY.py:2106 - Raw ^IRX columns: ['Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
2025-09-21 13:43:04,280 - INFO - SPY.py:2108 - Raw ^IRX Close values - first: 4.188000202178955, last: 4.239999771118164
2025-09-21 13:43:04,281 - INFO - SPY.py:2045 - Applying standardized Treasury rate validation for IRX (3-month Treasury yield)
2025-09-21 13:43:04,282 - INFO - SPY.py:2046 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-09-21 13:43:04,282 - INFO - SPY.py:2055 - IRX (3-month Treasury yield) converted from percentage to decimal format
2025-09-21 13:43:04,283 - INFO - SPY.py:2058 - IRX (3-month Treasury yield) after conversion to decimal: min=0.041500, max=0.042850, median=0.042080
2025-09-21 13:43:04,284 - INFO - SPY.py:2187 - Processed IRX (3-month Treasury yield) close values - first: 0.04188000202178955, last: 0.04239999771118164
2025-09-21 13:43:04,284 - INFO - SPY.py:2194 - VALIDATION: Final IRX (3-month Treasury yield) rate (decimal format): 0.042400 (4.2400%)
2025-09-21 13:43:04,285 - INFO - SPY.py:2220 - Market index ^IRX: Keeping only ['close_IRX']
2025-09-21 13:43:04,286 - INFO - SPY.py:2260 - Processed ^IRX data shape: (63, 1). Columns: ['close_IRX']
2025-09-21 13:43:04,286 - INFO - SPY.py:2261 - Data quality check: 0/63 (0.0%) zero values
2025-09-21 13:43:04,286 - INFO - SPY.py:982 - VALIDATION: ^IRX last value: 0.04239999771118164
2025-09-21 13:43:04,286 - INFO - SPY.py:712 - [INFO] PerformanceMonitor: Operation fetch_refactored_^IRX_1758433381 SUCCESS in 2.84s [Ticker: ^IRX] [Operation: refactored_fetch]
2025-09-21 13:43:05,088 - INFO - SPY.py:1760 - Attempt 1/3 with timeout 20s
2025-09-21 13:43:06,702 - INFO - SPY.py:2081 - Using min_periods_override=50 for ^TNX
2025-09-21 13:43:06,704 - INFO - SPY.py:2045 - Applying standardized Treasury rate validation for TNX (10-year Treasury yield)
2025-09-21 13:43:06,704 - INFO - SPY.py:2046 - ASSUMPTION: Input data is in percentage format, will convert to decimal format
2025-09-21 13:43:06,705 - INFO - SPY.py:2055 - TNX (10-year Treasury yield) converted from percentage to decimal format
2025-09-21 13:43:06,706 - INFO - SPY.py:2058 - TNX (10-year Treasury yield) after conversion to decimal: min=0.039850, max=0.045960, median=0.043650
2025-09-21 13:43:06,706 - INFO - SPY.py:2187 - Processed TNX (10-year Treasury yield) close values - first: 0.04306000232696533, last: 0.04423999786376953
2025-09-21 13:43:06,706 - INFO - SPY.py:2194 - VALIDATION: Final TNX (10-year Treasury yield) rate (decimal format): 0.044240 (4.4240%)
2025-09-21 13:43:06,707 - INFO - SPY.py:2220 - Market index ^TNX: Keeping only ['close_TNX']
2025-09-21 13:43:06,709 - INFO - SPY.py:2260 - Processed ^TNX data shape: (63, 1). Columns: ['close_TNX']
2025-09-21 13:43:06,709 - INFO - SPY.py:2261 - Data quality check: 0/63 (0.0%) zero values
2025-09-21 13:43:06,709 - INFO - SPY.py:982 - VALIDATION: ^TNX last value: 0.04423999786376953
2025-09-21 13:43:06,709 - INFO - SPY.py:712 - [INFO] PerformanceMonitor: Operation fetch_refactored_^TNX_1758433384 SUCCESS in 2.42s [Ticker: ^TNX] [Operation: refactored_fetch]
2025-09-21 13:43:06,714 - INFO - SPY.py:2424 - Created master index with 63 unique dates from 2025-03-17 to 2025-06-13
2025-09-21 13:43:06,714 - INFO - SPY.py:2429 - Processing ticker SPY for reindexing: shape=(63, 5), columns=['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY']
2025-09-21 13:43:06,715 - INFO - SPY.py:2435 - About to reindex SPY with master_index length 63
2025-09-21 13:43:06,715 - INFO - SPY.py:2321 - Index overlap for SPY: 100.0% (63/63 dates)
2025-09-21 13:43:06,718 - INFO - SPY.py:2384 - Successfully reindexed SPY with validation passed
2025-09-21 13:43:06,719 - INFO - SPY.py:2439 - Successfully reindexed SPY to master index. New shape: (63, 5)
2025-09-21 13:43:06,719 - INFO - SPY.py:2429 - Processing ticker ^VIX for reindexing: shape=(63, 1), columns=['close_VIX']
2025-09-21 13:43:06,720 - INFO - SPY.py:2435 - About to reindex ^VIX with master_index length 63
2025-09-21 13:43:06,720 - INFO - SPY.py:2321 - Index overlap for ^VIX: 100.0% (63/63 dates)
2025-09-21 13:43:06,721 - INFO - SPY.py:2384 - Successfully reindexed ^VIX with validation passed
2025-09-21 13:43:06,721 - INFO - SPY.py:2439 - Successfully reindexed ^VIX to master index. New shape: (63, 1)
2025-09-21 13:43:06,722 - INFO - SPY.py:2429 - Processing ticker ^VIX3M for reindexing: shape=(63, 1), columns=['close_VIX3M']
2025-09-21 13:43:06,722 - INFO - SPY.py:2435 - About to reindex ^VIX3M with master_index length 63
2025-09-21 13:43:06,722 - INFO - SPY.py:2321 - Index overlap for ^VIX3M: 100.0% (63/63 dates)
2025-09-21 13:43:06,723 - INFO - SPY.py:2384 - Successfully reindexed ^VIX3M with validation passed
2025-09-21 13:43:06,723 - INFO - SPY.py:2439 - Successfully reindexed ^VIX3M to master index. New shape: (63, 1)
2025-09-21 13:43:06,723 - INFO - SPY.py:2429 - Processing ticker ^IRX for reindexing: shape=(63, 1), columns=['close_IRX']
2025-09-21 13:43:06,724 - INFO - SPY.py:2435 - About to reindex ^IRX with master_index length 63
2025-09-21 13:43:06,724 - INFO - SPY.py:2321 - Index overlap for ^IRX: 100.0% (63/63 dates)
2025-09-21 13:43:06,724 - INFO - SPY.py:2384 - Successfully reindexed ^IRX with validation passed
2025-09-21 13:43:06,725 - INFO - SPY.py:2439 - Successfully reindexed ^IRX to master index. New shape: (63, 1)
2025-09-21 13:43:06,725 - INFO - SPY.py:2429 - Processing ticker ^TNX for reindexing: shape=(63, 1), columns=['close_TNX']
2025-09-21 13:43:06,725 - INFO - SPY.py:2435 - About to reindex ^TNX with master_index length 63
2025-09-21 13:43:06,725 - INFO - SPY.py:2321 - Index overlap for ^TNX: 100.0% (63/63 dates)
2025-09-21 13:43:06,726 - INFO - SPY.py:2384 - Successfully reindexed ^TNX with validation passed
2025-09-21 13:43:06,727 - INFO - SPY.py:2439 - Successfully reindexed ^TNX to master index. New shape: (63, 1)
2025-09-21 13:43:06,727 - INFO - SPY.py:2457 - Starting combine features with SPY shape: (63, 5)
2025-09-21 13:43:06,727 - INFO - SPY.py:2458 - Available tickers in reindexed_data_dict: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX']
2025-09-21 13:43:06,727 - INFO - SPY.py:2473 - Merging ^VIX (Shape: (63, 1), Columns: ['close_VIX'])
2025-09-21 13:43:06,728 - INFO - SPY.py:2483 - Columns for ^VIX already appear to be renamed. Using as-is.
2025-09-21 13:43:06,728 - INFO - SPY.py:2519 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-21 13:43:06,728 - INFO - SPY.py:2520 - ^VIX index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-21 13:43:06,729 - INFO - SPY.py:2537 - ^VIX close_VIX sample after join (first 3): [20.510000228881836, 21.700000762939453, 19.899999618530273], last: 20.81999969482422
2025-09-21 13:43:06,730 - INFO - SPY.py:2555 - After joining ^VIX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX']
2025-09-21 13:43:06,730 - INFO - SPY.py:2473 - Merging ^VIX3M (Shape: (63, 1), Columns: ['close_VIX3M'])
2025-09-21 13:43:06,730 - INFO - SPY.py:2483 - Columns for ^VIX3M already appear to be renamed. Using as-is.
2025-09-21 13:43:06,731 - INFO - SPY.py:2519 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-21 13:43:06,731 - INFO - SPY.py:2520 - ^VIX3M index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-21 13:43:06,732 - INFO - SPY.py:2537 - ^VIX3M close_VIX3M sample after join (first 3): [21.299999237060547, 21.969999313354492, 20.829999923706055], last: 22.6200008392334
2025-09-21 13:43:06,732 - INFO - SPY.py:2555 - After joining ^VIX3M, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX', 'close_VIX3M']
2025-09-21 13:43:06,732 - INFO - SPY.py:2473 - Merging ^IRX (Shape: (63, 1), Columns: ['close_IRX'])
2025-09-21 13:43:06,733 - INFO - SPY.py:2483 - Columns for ^IRX already appear to be renamed. Using as-is.
2025-09-21 13:43:06,733 - INFO - SPY.py:2519 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-21 13:43:06,734 - INFO - SPY.py:2520 - ^IRX index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-21 13:43:06,734 - INFO - SPY.py:2537 - ^IRX close_IRX sample after join (first 3): [0.04188000202178955, 0.04195000171661377, 0.04190000057220459], last: 0.04239999771118164
2025-09-21 13:43:06,735 - INFO - SPY.py:2555 - After joining ^IRX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX']
2025-09-21 13:43:06,735 - INFO - SPY.py:2473 - Merging ^TNX (Shape: (63, 1), Columns: ['close_TNX'])
2025-09-21 13:43:06,735 - INFO - SPY.py:2483 - Columns for ^TNX already appear to be renamed. Using as-is.
2025-09-21 13:43:06,736 - INFO - SPY.py:2519 - SPY index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-21 13:43:06,736 - INFO - SPY.py:2520 - ^TNX index range: 2025-03-17 to 2025-06-13, count: 63
2025-09-21 13:43:06,737 - INFO - SPY.py:2537 - ^TNX close_TNX sample after join (first 3): [0.04306000232696533, 0.042810001373291016, 0.042560000419616696], last: 0.04423999786376953
2025-09-21 13:43:06,737 - INFO - SPY.py:2555 - After joining ^TNX, combined_df columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX', 'close_VIX3M', 'close_IRX', 'close_TNX']
2025-09-21 13:43:06,737 - INFO - SPY.py:2566 - Shape after merging all tickers: (63, 9)
2025-09-21 13:43:06,738 - INFO - SPY.py:2721 - Skipping VIX derived feature calculations - using reduced feature set
2025-09-21 13:43:06,738 - INFO - SPY.py:2725 - Skipping Treasury derived feature calculations - using reduced feature set
2025-09-21 13:43:06,738 - INFO - SPY.py:2620 - Skipping derived feature validation - using reduced feature set
2025-09-21 13:43:06,739 - INFO - SPY.py:2625 - Applying normalization to volume_SPY feature...
2025-09-21 13:43:06,739 - INFO - SPY.py:2642 - Volume normalization applied:
2025-09-21 13:43:06,739 - INFO - SPY.py:2643 -   Original volume range: 37603400 to 256611400
2025-09-21 13:43:06,740 - INFO - SPY.py:2644 -   Log-transformed range: 17.4426 to 19.3631
2025-09-21 13:43:06,740 - INFO - SPY.py:2645 -   Normalized range: -1.6874 to 3.2291
2025-09-21 13:43:06,740 - INFO - SPY.py:2646 -   Normalized mean: 0.0000, std: 1.0000
2025-09-21 13:43:06,740 - INFO - SPY.py:2653 - Performing final validation and cleanup...
2025-09-21 13:43:06,741 - INFO - SPY.py:2672 - Excluding close_VIX3M from expected columns validation (will be removed from final feature set)
2025-09-21 13:43:06,741 - INFO - SPY.py:2774 - Feature distribution verification completed successfully
2025-09-21 13:43:06,741 - INFO - SPY.py:2699 - Removing close_VIX3M from final feature set to reduce from 9 to 8 features
2025-09-21 13:43:06,742 - INFO - SPY.py:2701 - Successfully removed close_VIX3M. VIX3M data collection logic remains intact for other uses.
2025-09-21 13:43:06,742 - INFO - SPY.py:2705 - Final combined features shape: (63, 8)
2025-09-21 13:43:06,742 - INFO - SPY.py:2706 - Final columns: ['open_SPY', 'high_SPY', 'low_SPY', 'close_SPY', 'volume_SPY', 'close_VIX', 'close_IRX', 'close_TNX']
2025-09-21 13:43:06,742 - INFO - SPY.py:4833 - N_MARKET_FEATURES: 8
2025-09-21 13:43:06,742 - INFO - SPY.py:4838 - === DETERMINISTIC MODEL SELECTION FOR SIGNAL GENERATION ===
2025-09-21 13:43:06,742 - INFO - SPY.py:6222 - === FINAL TRAINED MODEL STATUS ===
2025-09-21 13:43:06,743 - ERROR - SPY.py:6237 -   Final Model: [NOT FOUND]
2025-09-21 13:43:06,743 - INFO - SPY.py:6239 - === END MODEL STATUS ===
2025-09-21 13:43:06,743 - ERROR - SPY.py:6280 - MODEL_SELECTION: [ERROR] Final trained model not found: C:\Users\<USER>\Desktop\All use default\models_options_v1\ppo_spy_option_trader_v1.zip
2025-09-21 13:43:06,743 - ERROR - SPY.py:4842 - SIGNAL_GENERATION: No suitable model found for signal generation
2025-09-21 13:43:06,743 - INFO - SPY.py:8067 - [INFO] Main: Script execution completed
2025-09-21 13:43:06,743 - INFO - SPY.py:757 - [INFO] PerformanceMonitor: Performance Summary: 5/5 operations successful (100.0% success rate)
2025-09-21 13:43:06,744 - INFO - SPY.py:765 - [INFO] PerformanceMonitor: Average refactored_fetch time: 6.44s
